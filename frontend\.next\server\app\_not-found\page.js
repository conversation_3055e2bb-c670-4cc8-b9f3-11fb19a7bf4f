(()=>{var e={};e.id=409,e.ids=[409],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4791:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>c,pages:()=>u,routeModule:()=>f,tree:()=>d}),n(7352),n(5866),n(2029);var o=n(3191),r=n(8716),a=n(7922),i=n.n(a),s=n(5231),l={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);n.d(t,l);let d=["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.t.bind(n,5866,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,2029)),"C:\\Choreo\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,5866,23)),"next/dist/client/components/not-found-error"]}],u=[],c="/_not-found/page",p={require:n,loadChunk:()=>Promise.resolve()},f=new o.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},4015:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,2994,23)),Promise.resolve().then(n.t.bind(n,6114,23)),Promise.resolve().then(n.t.bind(n,9727,23)),Promise.resolve().then(n.t.bind(n,9671,23)),Promise.resolve().then(n.t.bind(n,1868,23)),Promise.resolve().then(n.t.bind(n,4759,23))},469:()=>{},6399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{isNotFoundError:function(){return r},notFound:function(){return o}});let n="NEXT_NOT_FOUND";function o(){let e=Error(n);throw e.digest=n,e}function r(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===n}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7352:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{PARALLEL_ROUTE_DEFAULT_PATH:function(){return r},default:function(){return a}});let o=n(6399),r="next/dist/client/components/parallel-route-default.js";function a(){(0,o.notFound)()}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2029:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>s,metadata:()=>i});var o=n(9510),r=n(5384),a=n.n(r);n(5023);let i={title:"Task Management - Choreo Full-Stack Sample",description:"A comprehensive task management application built with Next.js and deployed on WSO2 Choreo",keywords:["task management","choreo","nextjs","wso2","productivity"],authors:[{name:"WSO2 Choreo Team"}],viewport:"width=device-width, initial-scale=1",robots:"index, follow",openGraph:{title:"Task Management - Choreo Full-Stack Sample",description:"A comprehensive task management application built with Next.js and deployed on WSO2 Choreo",type:"website",locale:"en_US"},twitter:{card:"summary_large_image",title:"Task Management - Choreo Full-Stack Sample",description:"A comprehensive task management application built with Next.js and deployed on WSO2 Choreo"}};function s({children:e}){return o.jsx("html",{lang:"en",className:"h-full",children:o.jsx("body",{className:`${a().className} h-full`,children:o.jsx("div",{id:"root",className:"h-full",children:e})})})}},5023:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),o=t.X(0,[935],()=>n(4791));module.exports=o})();