(()=>{var e={};e.id=931,e.ids=[931],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},8721:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>u,routeModule:()=>f,tree:()=>c}),r(5480),r(2029),r(5866);var n=r(3191),a=r(8716),s=r(7922),o=r.n(s),i=r(5231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5480)),"C:\\Choreo\\frontend\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,2029)),"C:\\Choreo\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"]}],u=["C:\\Choreo\\frontend\\src\\app\\page.tsx"],d="/page",p={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},4015:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2994,23)),Promise.resolve().then(r.t.bind(r,6114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,9671,23)),Promise.resolve().then(r.t.bind(r,1868,23)),Promise.resolve().then(r.t.bind(r,4759,23))},469:()=>{},4625:(e,t,r)=>{Promise.resolve().then(r.bind(r,1255))},6557:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(7577),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),o=(e,t)=>{let r=(0,n.forwardRef)(({color:r="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:l,className:c="",children:u,...d},p)=>(0,n.createElement)("svg",{ref:p,...a,width:o,height:o,stroke:r,strokeWidth:l?24*Number(i)/Number(o):i,className:["lucide",`lucide-${s(e)}`,c].join(" "),...d},[...t.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(u)?u:[u]]));return r.displayName=`${e}`,r}},3416:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return n}}),r(3658);let n=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9683:(e,t,r)=>{"use strict";function n(e,t,r,n){return!1}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDomainLocale",{enumerable:!0,get:function(){return n}}),r(3658),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9404:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return _}});let n=r(1174),a=r(326),s=n._(r(7577)),o=r(5619),i=r(944),l=r(3071),c=r(1348),u=r(3416),d=r(131),p=r(2413),f=r(9408),h=r(9683),m=r(3486),x=r(7767);function g(e){return"string"==typeof e?e:(0,l.formatUrl)(e)}let _=s.default.forwardRef(function(e,t){let r,n;let{href:l,as:_,children:y,prefetch:b=null,passHref:E,replace:N,shallow:j,scroll:R,locale:v,onClick:S,onMouseEnter:P,onTouchStart:w,legacyBehavior:A=!1,...O}=e;r=y,A&&("string"==typeof r||"number"==typeof r)&&(r=(0,a.jsx)("a",{children:r}));let T=s.default.useContext(d.RouterContext),C=s.default.useContext(p.AppRouterContext),I=null!=T?T:C,M=!T,k=!1!==b,L=null===b?x.PrefetchKind.AUTO:x.PrefetchKind.FULL,{href:D,as:U}=s.default.useMemo(()=>{if(!T){let e=g(l);return{href:e,as:_?g(_):e}}let[e,t]=(0,o.resolveHref)(T,l,!0);return{href:e,as:_?(0,o.resolveHref)(T,_):t||e}},[T,l,_]),F=s.default.useRef(D),W=s.default.useRef(U);A&&(n=s.default.Children.only(r));let G=A?n&&"object"==typeof n&&n.ref:t,[X,H,B]=(0,f.useIntersection)({rootMargin:"200px"}),Y=s.default.useCallback(e=>{(W.current!==U||F.current!==D)&&(B(),W.current=U,F.current=D),X(e),G&&("function"==typeof G?G(e):"object"==typeof G&&(G.current=e))},[U,G,D,B,X]);s.default.useEffect(()=>{},[U,D,H,v,k,null==T?void 0:T.locale,I,M,L]);let q={ref:Y,onClick(e){A||"function"!=typeof S||S(e),A&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),I&&!e.defaultPrevented&&function(e,t,r,n,a,o,l,c,u){let{nodeName:d}=e.currentTarget;if("A"===d.toUpperCase()&&(function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||!u&&!(0,i.isLocalURL)(r)))return;e.preventDefault();let p=()=>{let e=null==l||l;"beforePopState"in t?t[a?"replace":"push"](r,n,{shallow:o,locale:c,scroll:e}):t[a?"replace":"push"](n||r,{scroll:e})};u?s.default.startTransition(p):p()}(e,I,D,U,N,j,R,v,M)},onMouseEnter(e){A||"function"!=typeof P||P(e),A&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e)},onTouchStart:function(e){A||"function"!=typeof w||w(e),A&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e)}};if((0,c.isAbsoluteUrl)(U))q.href=U;else if(!A||E||"a"===n.type&&!("href"in n.props)){let e=void 0!==v?v:null==T?void 0:T.locale,t=(null==T?void 0:T.isLocaleDomain)&&(0,h.getDomainLocale)(U,e,null==T?void 0:T.locales,null==T?void 0:T.domainLocales);q.href=t||(0,m.addBasePath)((0,u.addLocale)(U,e,null==T?void 0:T.defaultLocale))}return A?s.default.cloneElement(n,q):(0,a.jsx)("a",{...O,...q,children:r})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},956:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return n},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5619:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"resolveHref",{enumerable:!0,get:function(){return d}});let n=r(2149),a=r(3071),s=r(757),o=r(1348),i=r(3658),l=r(944),c=r(4903),u=r(1394);function d(e,t,r){let d;let p="string"==typeof t?t:(0,a.formatWithValidation)(t),f=p.match(/^[a-zA-Z]{1,}:\/\//),h=f?p.slice(f[0].length):p;if((h.split("?",1)[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href '"+p+"' passed to next/router in page: '"+e.pathname+"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href.");let t=(0,o.normalizeRepeatedSlashes)(h);p=(f?f[0]:"")+t}if(!(0,l.isLocalURL)(p))return r?[p]:p;try{d=new URL(p.startsWith("#")?e.asPath:e.pathname,"http://n")}catch(e){d=new URL("/","http://n")}try{let e=new URL(p,d);e.pathname=(0,i.normalizePathTrailingSlash)(e.pathname);let t="";if((0,c.isDynamicRoute)(e.pathname)&&e.searchParams&&r){let r=(0,n.searchParamsToUrlQuery)(e.searchParams),{result:o,params:i}=(0,u.interpolateAs)(e.pathname,e.pathname,r);o&&(t=(0,a.formatWithValidation)({pathname:o,hash:e.hash,query:(0,s.omit)(r,i)}))}let o=e.origin===d.origin?e.href.slice(e.origin.length):e.href;return r?[o,t||o]:o}catch(e){return r?[p]:p}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9408:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return l}});let n=r(7577),a=r(956),s="function"==typeof IntersectionObserver,o=new Map,i=[];function l(e){let{rootRef:t,rootMargin:r,disabled:l}=e,c=l||!s,[u,d]=(0,n.useState)(!1),p=(0,n.useRef)(null),f=(0,n.useCallback)(e=>{p.current=e},[]);return(0,n.useEffect)(()=>{if(s){if(c||u)return;let e=p.current;if(e&&e.tagName)return function(e,t,r){let{id:n,observer:a,elements:s}=function(e){let t;let r={root:e.root||null,margin:e.rootMargin||""},n=i.find(e=>e.root===r.root&&e.margin===r.margin);if(n&&(t=o.get(n)))return t;let a=new Map;return t={id:r,observer:new IntersectionObserver(e=>{e.forEach(e=>{let t=a.get(e.target),r=e.isIntersecting||e.intersectionRatio>0;t&&r&&t(r)})},e),elements:a},i.push(r),o.set(r,t),t}(r);return s.set(e,t),a.observe(e),function(){if(s.delete(e),a.unobserve(e),0===s.size){a.disconnect(),o.delete(n);let e=i.findIndex(e=>e.root===n.root&&e.margin===n.margin);e>-1&&i.splice(e,1)}}}(e,e=>e&&d(e),{root:null==t?void 0:t.current,rootMargin:r})}else if(!u){let e=(0,a.requestIdleCallback)(()=>d(!0));return()=>(0,a.cancelIdleCallback)(e)}},[c,r,t,u,p.current]),[f,u,(0,n.useCallback)(()=>{d(!1)},[])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5633:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_SUFFIX:function(){return l},APP_DIR_ALIAS:function(){return P},CACHE_ONE_YEAR:function(){return b},DOT_NEXT_ALIAS:function(){return v},ESLINT_DEFAULT_DIRS:function(){return Y},GSP_NO_RETURNED_VALUE:function(){return F},GSSP_COMPONENT_MEMBER_ERROR:function(){return X},GSSP_NO_RETURNED_VALUE:function(){return W},INSTRUMENTATION_HOOK_FILENAME:function(){return j},MIDDLEWARE_FILENAME:function(){return E},MIDDLEWARE_LOCATION_REGEXP:function(){return N},NEXT_BODY_SUFFIX:function(){return d},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return y},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return h},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return m},NEXT_CACHE_SOFT_TAGS_HEADER:function(){return f},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return _},NEXT_CACHE_TAGS_HEADER:function(){return p},NEXT_CACHE_TAG_MAX_ITEMS:function(){return x},NEXT_CACHE_TAG_MAX_LENGTH:function(){return g},NEXT_DATA_SUFFIX:function(){return c},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return n},NEXT_META_SUFFIX:function(){return u},NEXT_QUERY_PARAM_PREFIX:function(){return r},NON_STANDARD_NODE_ENV:function(){return H},PAGES_DIR_ALIAS:function(){return R},PRERENDER_REVALIDATE_HEADER:function(){return a},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return s},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return I},ROOT_DIR_ALIAS:function(){return S},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return C},RSC_ACTION_ENCRYPTION_ALIAS:function(){return T},RSC_ACTION_PROXY_ALIAS:function(){return O},RSC_ACTION_VALIDATE_ALIAS:function(){return A},RSC_MOD_REF_PROXY_ALIAS:function(){return w},RSC_PREFETCH_SUFFIX:function(){return o},RSC_SUFFIX:function(){return i},SERVER_PROPS_EXPORT_ERROR:function(){return U},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return k},SERVER_PROPS_SSG_CONFLICT:function(){return L},SERVER_RUNTIME:function(){return q},SSG_FALLBACK_EXPORT_ERROR:function(){return B},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return M},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return D},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return G},WEBPACK_LAYERS:function(){return K},WEBPACK_RESOURCE_QUERIES:function(){return z}});let r="nxtP",n="nxtI",a="x-prerender-revalidate",s="x-prerender-revalidate-if-generated",o=".prefetch.rsc",i=".rsc",l=".action",c=".json",u=".meta",d=".body",p="x-next-cache-tags",f="x-next-cache-soft-tags",h="x-next-revalidated-tags",m="x-next-revalidate-tag-token",x=128,g=256,_=1024,y="_N_T_",b=31536e3,E="middleware",N=`(?:src/)?${E}`,j="instrumentation",R="private-next-pages",v="private-dot-next",S="private-next-root-dir",P="private-next-app-dir",w="private-next-rsc-mod-ref-proxy",A="private-next-rsc-action-validate",O="private-next-rsc-server-reference",T="private-next-rsc-action-encryption",C="private-next-rsc-action-client-wrapper",I="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",M="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",k="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",L="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",D="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",U="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",F="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",W="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",G="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",X="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",H='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',B="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",Y=["app","pages","components","lib","src"],q={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},V={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",api:"api",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",appMetadataRoute:"app-metadata-route",appRouteHandler:"app-route-handler"},K={...V,GROUP:{serverOnly:[V.reactServerComponents,V.actionBrowser,V.appMetadataRoute,V.appRouteHandler,V.instrument],clientOnly:[V.serverSideRendering,V.appPagesBrowser],nonClientServerTarget:[V.middleware,V.api],app:[V.reactServerComponents,V.actionBrowser,V.appMetadataRoute,V.appRouteHandler,V.serverSideRendering,V.appPagesBrowser,V.shared,V.instrument]}},z={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},131:(e,t,r)=>{"use strict";e.exports=r(1616).vendored.contexts.RouterContext},2451:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return a}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function a(e){return r.test(e)?e.replace(n,"\\$&"):e}},3071:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return s},formatWithValidation:function(){return i},urlObjectKeys:function(){return o}});let n=r(8374)._(r(2149)),a=/https?|ftp|gopher|file/;function s(e){let{auth:t,hostname:r}=e,s=e.protocol||"",o=e.pathname||"",i=e.hash||"",l=e.query||"",c=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?c=t+e.host:r&&(c=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(c+=":"+e.port)),l&&"object"==typeof l&&(l=String(n.urlQueryToSearchParams(l)));let u=e.search||l&&"?"+l||"";return s&&!s.endsWith(":")&&(s+=":"),e.slashes||(!s||a.test(s))&&!1!==c?(c="//"+(c||""),o&&"/"!==o[0]&&(o="/"+o)):c||(c=""),i&&"#"!==i[0]&&(i="#"+i),u&&"?"!==u[0]&&(u="?"+u),""+s+c+(o=o.replace(/[?#]/g,encodeURIComponent))+(u=u.replace("#","%23"))+i}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function i(e){return s(e)}},4903:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRoutes:function(){return n.getSortedRoutes},isDynamicRoute:function(){return a.isDynamicRoute}});let n=r(4712),a=r(5541)},1394:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"interpolateAs",{enumerable:!0,get:function(){return s}});let n=r(9966),a=r(7249);function s(e,t,r){let s="",o=(0,a.getRouteRegex)(e),i=o.groups,l=(t!==e?(0,n.getRouteMatcher)(o)(t):"")||r;s=e;let c=Object.keys(i);return c.every(e=>{let t=l[e]||"",{repeat:r,optional:n}=i[e],a="["+(r?"...":"")+e+"]";return n&&(a=(t?"":"/")+"["+a+"]"),r&&!Array.isArray(t)&&(t=[t]),(n||e in l)&&(s=s.replace(a,r?t.map(e=>encodeURIComponent(e)).join("/"):encodeURIComponent(t))||"/")})||(s=""),{params:c,result:s}}},5541:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isDynamicRoute",{enumerable:!0,get:function(){return s}});let n=r(7356),a=/\/\[[^/]+?\](?=\/|$)/;function s(e){return(0,n.isInterceptionRouteAppPath)(e)&&(e=(0,n.extractInterceptionRouteInformation)(e).interceptedRoute),a.test(e)}},944:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return s}});let n=r(1348),a=r(7929);function s(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,a.hasBasePath)(r.pathname)}catch(e){return!1}}},757:(e,t)=>{"use strict";function r(e,t){let r={};return Object.keys(e).forEach(n=>{t.includes(n)||(r[n]=e[n])}),r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"omit",{enumerable:!0,get:function(){return r}})},2149:(e,t)=>{"use strict";function r(e){let t={};return e.forEach((e,r)=>{void 0===t[r]?t[r]=e:Array.isArray(t[r])?t[r].push(e):t[r]=[t[r],e]}),t}function n(e){return"string"!=typeof e&&("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function a(e){let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,a]=e;Array.isArray(a)?a.forEach(e=>t.append(r,n(e))):t.set(r,n(a))}),t}function s(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return r.forEach(t=>{Array.from(t.keys()).forEach(t=>e.delete(t)),t.forEach((t,r)=>e.append(r,t))}),e}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return s},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return a}})},9966:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return a}});let n=r(1348);function a(e){let{re:t,groups:r}=e;return e=>{let a=t.exec(e);if(!a)return!1;let s=e=>{try{return decodeURIComponent(e)}catch(e){throw new n.DecodeError("failed to decode param")}},o={};return Object.keys(r).forEach(e=>{let t=r[e],n=a[t.pos];void 0!==n&&(o[e]=~n.indexOf("/")?n.split("/").map(e=>s(e)):t.repeat?[s(n)]:s(n))}),o}}},7249:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return f},getNamedRouteRegex:function(){return p},getRouteRegex:function(){return c},parseParameter:function(){return i}});let n=r(5633),a=r(7356),s=r(2451),o=r(3236);function i(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function l(e){let t=(0,o.removeTrailingSlash)(e).slice(1).split("/"),r={},n=1;return{parameterizedRoute:t.map(e=>{let t=a.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t)),o=e.match(/\[((?:\[.*\])|.+)\]/);if(t&&o){let{key:e,optional:a,repeat:l}=i(o[1]);return r[e]={pos:n++,repeat:l,optional:a},"/"+(0,s.escapeStringRegexp)(t)+"([^/]+?)"}if(!o)return"/"+(0,s.escapeStringRegexp)(e);{let{key:e,repeat:t,optional:a}=i(o[1]);return r[e]={pos:n++,repeat:t,optional:a},t?a?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:r}}function c(e){let{parameterizedRoute:t,groups:r}=l(e);return{re:RegExp("^"+t+"(?:/)?$"),groups:r}}function u(e){let{interceptionMarker:t,getSafeRouteKey:r,segment:n,routeKeys:a,keyPrefix:o}=e,{key:l,optional:c,repeat:u}=i(n),d=l.replace(/\W/g,"");o&&(d=""+o+d);let p=!1;(0===d.length||d.length>30)&&(p=!0),isNaN(parseInt(d.slice(0,1)))||(p=!0),p&&(d=r()),o?a[d]=""+o+l:a[d]=l;let f=t?(0,s.escapeStringRegexp)(t):"";return u?c?"(?:/"+f+"(?<"+d+">.+?))?":"/"+f+"(?<"+d+">.+?)":"/"+f+"(?<"+d+">[^/]+?)"}function d(e,t){let r;let i=(0,o.removeTrailingSlash)(e).slice(1).split("/"),l=(r=0,()=>{let e="",t=++r;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),c={};return{namedParameterizedRoute:i.map(e=>{let r=a.INTERCEPTION_ROUTE_MARKERS.some(t=>e.startsWith(t)),o=e.match(/\[((?:\[.*\])|.+)\]/);if(r&&o){let[r]=e.split(o[0]);return u({getSafeRouteKey:l,interceptionMarker:r,segment:o[1],routeKeys:c,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0})}return o?u({getSafeRouteKey:l,segment:o[1],routeKeys:c,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0}):"/"+(0,s.escapeStringRegexp)(e)}).join(""),routeKeys:c}}function p(e,t){let r=d(e,t);return{...c(e),namedRegex:"^"+r.namedParameterizedRoute+"(?:/)?$",routeKeys:r.routeKeys}}function f(e,t){let{parameterizedRoute:r}=l(e),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:a}=d(e,!1);return{namedRegex:"^"+a+(n?"(?:(/.*)?)":"")+"$"}}},4712:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSortedRoutes",{enumerable:!0,get:function(){return n}});class r{insert(e){this._insert(e.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){void 0===e&&(e="/");let t=[...this.children.keys()].sort();null!==this.slugName&&t.splice(t.indexOf("[]"),1),null!==this.restSlugName&&t.splice(t.indexOf("[...]"),1),null!==this.optionalRestSlugName&&t.splice(t.indexOf("[[...]]"),1);let r=t.map(t=>this.children.get(t)._smoosh(""+e+t+"/")).reduce((e,t)=>[...e,...t],[]);if(null!==this.slugName&&r.push(...this.children.get("[]")._smoosh(e+"["+this.slugName+"]/")),!this.placeholder){let t="/"===e?"/":e.slice(0,-1);if(null!=this.optionalRestSlugName)throw Error('You cannot define a route with the same specificity as a optional catch-all route ("'+t+'" and "'+t+"[[..."+this.optionalRestSlugName+']]").');r.unshift(t)}return null!==this.restSlugName&&r.push(...this.children.get("[...]")._smoosh(e+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&r.push(...this.children.get("[[...]]")._smoosh(e+"[[..."+this.optionalRestSlugName+"]]/")),r}_insert(e,t,n){if(0===e.length){this.placeholder=!1;return}if(n)throw Error("Catch-all must be the last part of the URL.");let a=e[0];if(a.startsWith("[")&&a.endsWith("]")){let r=a.slice(1,-1),o=!1;if(r.startsWith("[")&&r.endsWith("]")&&(r=r.slice(1,-1),o=!0),r.startsWith("...")&&(r=r.substring(3),n=!0),r.startsWith("[")||r.endsWith("]"))throw Error("Segment names may not start or end with extra brackets ('"+r+"').");if(r.startsWith("."))throw Error("Segment names may not start with erroneous periods ('"+r+"').");function s(e,r){if(null!==e&&e!==r)throw Error("You cannot use different slug names for the same dynamic path ('"+e+"' !== '"+r+"').");t.forEach(e=>{if(e===r)throw Error('You cannot have the same slug name "'+r+'" repeat within a single dynamic path');if(e.replace(/\W/g,"")===a.replace(/\W/g,""))throw Error('You cannot have the slug names "'+e+'" and "'+r+'" differ only by non-word symbols within a single dynamic path')}),t.push(r)}if(n){if(o){if(null!=this.restSlugName)throw Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+e[0]+'" ).');s(this.optionalRestSlugName,r),this.optionalRestSlugName=r,a="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+e[0]+'").');s(this.restSlugName,r),this.restSlugName=r,a="[...]"}}else{if(o)throw Error('Optional route parameters are not yet supported ("'+e[0]+'").');s(this.slugName,r),this.slugName=r,a="[]"}}this.children.has(a)||this.children.set(a,new r),this.children.get(a)._insert(e.slice(1),t,n)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}function n(e){let t=new r;return e.forEach(e=>t.insert(e)),t.smoosh()}},1348:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return _},MissingStaticPage:function(){return g},NormalizeError:function(){return m},PageNotFoundError:function(){return x},SP:function(){return p},ST:function(){return f},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return o},getURL:function(){return i},isAbsoluteUrl:function(){return s},isResSent:function(){return c},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return u},stringifyError:function(){return y}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,a=Array(n),s=0;s<n;s++)a[s]=arguments[s];return r||(r=!0,t=e(...a)),t}}let a=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=e=>a.test(e);function o(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function i(){let{href:e}=window.location,t=o();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function u(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&c(r))return n;if(!n)throw Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.');return n}let p="undefined"!=typeof performance,f=p&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class m extends Error{}class x extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class _ extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function y(e){return JSON.stringify({message:e.message,stack:e.stack})}},1255:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var n=r(326),a=r(7577),s=r(6557);let o=(0,s.Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),i=(0,s.Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),l=(0,s.Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]),c=(0,s.Z)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]]),u=(0,s.Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),d=(0,s.Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var p=r(9404),f=r.n(p);function h(){let[e,t]=(0,a.useState)(!0);return e?n.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-100",children:(0,n.jsxs)("div",{className:"text-center",children:[n.jsx("div",{className:"spinner w-8 h-8 mx-auto mb-4"}),n.jsx("p",{className:"text-secondary-600",children:"Loading..."})]})}):(0,n.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100",children:[n.jsx("header",{className:"bg-white/80 backdrop-blur-sm border-b border-secondary-200 sticky top-0 z-50",children:n.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[n.jsx("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:n.jsx(o,{className:"w-5 h-5 text-white"})}),n.jsx("h1",{className:"text-xl font-bold text-secondary-900",children:"Task Management"})]}),n.jsx("nav",{className:"flex items-center space-x-4",children:(0,n.jsxs)(f(),{href:"/dashboard",className:"btn-primary flex items-center space-x-2",children:[n.jsx("span",{children:"Get Started"}),n.jsx(i,{className:"w-4 h-4"})]})})]})})}),(0,n.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,n.jsxs)("div",{className:"text-center mb-16",children:[(0,n.jsxs)("h2",{className:"text-4xl md:text-6xl font-bold text-secondary-900 mb-6 text-balance",children:["Manage Your Tasks with",n.jsx("span",{className:"text-primary-600 block",children:"WSO2 Choreo"})]}),n.jsx("p",{className:"text-xl text-secondary-600 mb-8 max-w-3xl mx-auto text-balance",children:"A comprehensive full-stack sample application demonstrating modern web development with Next.js frontend and Node.js backend, all deployed on WSO2 Choreo platform."}),(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[n.jsx(f(),{href:"/dashboard",className:"btn-primary text-lg px-8 py-3",children:"Start Managing Tasks"}),(0,n.jsxs)("a",{href:"https://github.com/your-org/choreo-fullstack-sample",target:"_blank",rel:"noopener noreferrer",className:"btn-outline text-lg px-8 py-3 flex items-center justify-center space-x-2",children:[n.jsx(l,{className:"w-5 h-5"}),n.jsx("span",{children:"View Source"}),n.jsx(c,{className:"w-4 h-4"})]})]})]}),(0,n.jsxs)("div",{className:"grid md:grid-cols-3 gap-8 mb-16",children:[(0,n.jsxs)("div",{className:"card p-8 text-center hover:shadow-medium transition-shadow duration-300",children:[n.jsx("div",{className:"w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:n.jsx(o,{className:"w-6 h-6 text-primary-600"})}),n.jsx("h3",{className:"text-xl font-semibold text-secondary-900 mb-3",children:"Task Management"}),n.jsx("p",{className:"text-secondary-600",children:"Create, update, and organize your tasks with an intuitive interface. Set priorities, due dates, and track progress effortlessly."})]}),(0,n.jsxs)("div",{className:"card p-8 text-center hover:shadow-medium transition-shadow duration-300",children:[n.jsx("div",{className:"w-12 h-12 bg-success-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:n.jsx(u,{className:"w-6 h-6 text-success-600"})}),n.jsx("h3",{className:"text-xl font-semibold text-secondary-900 mb-3",children:"User Authentication"}),n.jsx("p",{className:"text-secondary-600",children:"Secure authentication powered by Choreo's managed authentication system. No complex setup required - just focus on your application logic."})]}),(0,n.jsxs)("div",{className:"card p-8 text-center hover:shadow-medium transition-shadow duration-300",children:[n.jsx("div",{className:"w-12 h-12 bg-warning-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:n.jsx(d,{className:"w-6 h-6 text-warning-600"})}),n.jsx("h3",{className:"text-xl font-semibold text-secondary-900 mb-3",children:"Real-time Updates"}),n.jsx("p",{className:"text-secondary-600",children:"Experience seamless real-time updates with optimistic UI patterns and efficient API communication between frontend and backend."})]})]}),(0,n.jsxs)("div",{className:"card p-8 mb-16",children:[n.jsx("h3",{className:"text-2xl font-bold text-secondary-900 mb-6 text-center",children:"Built with Modern Technologies"}),(0,n.jsxs)("div",{className:"grid md:grid-cols-2 gap-8",children:[(0,n.jsxs)("div",{children:[n.jsx("h4",{className:"text-lg font-semibold text-secondary-900 mb-4",children:"Frontend"}),(0,n.jsxs)("ul",{className:"space-y-2 text-secondary-600",children:[(0,n.jsxs)("li",{className:"flex items-center space-x-2",children:[n.jsx(o,{className:"w-4 h-4 text-success-600"}),n.jsx("span",{children:"Next.js 14 with App Router"})]}),(0,n.jsxs)("li",{className:"flex items-center space-x-2",children:[n.jsx(o,{className:"w-4 h-4 text-success-600"}),n.jsx("span",{children:"TypeScript for type safety"})]}),(0,n.jsxs)("li",{className:"flex items-center space-x-2",children:[n.jsx(o,{className:"w-4 h-4 text-success-600"}),n.jsx("span",{children:"Tailwind CSS for styling"})]}),(0,n.jsxs)("li",{className:"flex items-center space-x-2",children:[n.jsx(o,{className:"w-4 h-4 text-success-600"}),n.jsx("span",{children:"Choreo managed authentication"})]})]})]}),(0,n.jsxs)("div",{children:[n.jsx("h4",{className:"text-lg font-semibold text-secondary-900 mb-4",children:"Backend"}),(0,n.jsxs)("ul",{className:"space-y-2 text-secondary-600",children:[(0,n.jsxs)("li",{className:"flex items-center space-x-2",children:[n.jsx(o,{className:"w-4 h-4 text-success-600"}),n.jsx("span",{children:"Node.js with Express.js"})]}),(0,n.jsxs)("li",{className:"flex items-center space-x-2",children:[n.jsx(o,{className:"w-4 h-4 text-success-600"}),n.jsx("span",{children:"RESTful API with OpenAPI spec"})]}),(0,n.jsxs)("li",{className:"flex items-center space-x-2",children:[n.jsx(o,{className:"w-4 h-4 text-success-600"}),n.jsx("span",{children:"JWT authentication integration"})]}),(0,n.jsxs)("li",{className:"flex items-center space-x-2",children:[n.jsx(o,{className:"w-4 h-4 text-success-600"}),n.jsx("span",{children:"Health monitoring endpoints"})]})]})]})]})]}),(0,n.jsxs)("div",{className:"text-center",children:[n.jsx("h3",{className:"text-2xl font-bold text-secondary-900 mb-4",children:"Ready to Get Started?"}),n.jsx("p",{className:"text-lg text-secondary-600 mb-8 max-w-2xl mx-auto",children:"Experience the power of full-stack development on WSO2 Choreo. Sign in to start managing your tasks and explore the application features."}),(0,n.jsxs)(f(),{href:"/dashboard",className:"btn-primary text-lg px-8 py-3 inline-flex items-center space-x-2",children:[n.jsx("span",{children:"Launch Application"}),n.jsx(i,{className:"w-5 h-5"})]})]})]}),n.jsx("footer",{className:"bg-white border-t border-secondary-200 mt-16",children:n.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:n.jsx("div",{className:"text-center text-secondary-600",children:(0,n.jsxs)("p",{children:["Built with ❤️ for the WSO2 Choreo community.",n.jsx("a",{href:"https://wso2.com/choreo/",target:"_blank",rel:"noopener noreferrer",className:"text-primary-600 hover:text-primary-700 ml-1",children:"Learn more about Choreo"})]})})})})]})}},2029:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>o});var n=r(9510),a=r(5384),s=r.n(a);r(5023);let o={title:"Task Management - Choreo Full-Stack Sample",description:"A comprehensive task management application built with Next.js and deployed on WSO2 Choreo",keywords:["task management","choreo","nextjs","wso2","productivity"],authors:[{name:"WSO2 Choreo Team"}],viewport:"width=device-width, initial-scale=1",robots:"index, follow",openGraph:{title:"Task Management - Choreo Full-Stack Sample",description:"A comprehensive task management application built with Next.js and deployed on WSO2 Choreo",type:"website",locale:"en_US"},twitter:{card:"summary_large_image",title:"Task Management - Choreo Full-Stack Sample",description:"A comprehensive task management application built with Next.js and deployed on WSO2 Choreo"}};function i({children:e}){return n.jsx("html",{lang:"en",className:"h-full",children:n.jsx("body",{className:`${s().className} h-full`,children:n.jsx("div",{id:"root",className:"h-full",children:e})})})}},5480:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(8570).createProxy)(String.raw`C:\Choreo\frontend\src\app\page.tsx#default`)},5023:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[935],()=>r(8721));module.exports=n})();