(()=>{var e={};e.id=590,e.ids=[590],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},6690:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>h,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c}),t(402),t(2029),t(5866);var a=t(3191),s=t(8716),n=t(7922),i=t.n(n),o=t(5231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let c=["",{children:["auth",{children:["error",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,402)),"C:\\Choreo\\frontend\\src\\app\\auth\\error\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,2029)),"C:\\Choreo\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,5866,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Choreo\\frontend\\src\\app\\auth\\error\\page.tsx"],u="/auth/error/page",h={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/auth/error/page",pathname:"/auth/error",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},4015:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,2994,23)),Promise.resolve().then(t.t.bind(t,6114,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,9671,23)),Promise.resolve().then(t.t.bind(t,1868,23)),Promise.resolve().then(t.t.bind(t,4759,23))},469:()=>{},2583:(e,r,t)=>{Promise.resolve().then(t.bind(t,6977))},6557:(e,r,t)=>{"use strict";t.d(r,{Z:()=>i});var a=t(7577),s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),i=(e,r)=>{let t=(0,a.forwardRef)(({color:t="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:l,className:c="",children:d,...u},h)=>(0,a.createElement)("svg",{ref:h,...s,width:i,height:i,stroke:t,strokeWidth:l?24*Number(o)/Number(i):o,className:["lucide",`lucide-${n(e)}`,c].join(" "),...u},[...r.map(([e,r])=>(0,a.createElement)(e,r)),...Array.isArray(d)?d:[d]]));return t.displayName=`${e}`,t}},5047:(e,r,t)=>{"use strict";var a=t(7389);t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},6977:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var a=t(326),s=t(7577),n=t(5047),i=t(6557);let o=(0,i.Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),l=(0,i.Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),c=(0,i.Z)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]);function d(){(0,n.useSearchParams)();let e=(0,n.useRouter)(),[r,t]=(0,s.useState)(null),[i,d]=(0,s.useState)(null);return a.jsx("div",{className:"min-h-screen bg-secondary-50 flex items-center justify-center px-4",children:a.jsx("div",{className:"max-w-md w-full",children:(0,a.jsxs)("div",{className:"card p-8 text-center",children:[a.jsx("div",{className:"w-16 h-16 bg-danger-100 rounded-full flex items-center justify-center mx-auto mb-6",children:a.jsx(o,{className:"w-8 h-8 text-danger-600"})}),a.jsx("h1",{className:"text-2xl font-bold text-secondary-900 mb-4",children:(e=>{switch(e){case"access_denied":return"Access Denied";case"invalid_request":return"Invalid Request";case"server_error":return"Server Error";case"temporarily_unavailable":return"Service Temporarily Unavailable";default:return"Authentication Error"}})(r)}),a.jsx("p",{className:"text-secondary-600 mb-8",children:((e,r)=>{if(r)return r;switch(e){case"access_denied":return"You have denied access to the application or do not have the required permissions.";case"invalid_request":return"The authentication request was invalid or malformed.";case"server_error":return"An internal server error occurred during authentication.";case"temporarily_unavailable":return"The authentication service is temporarily unavailable. Please try again later.";default:return"An unexpected error occurred during the authentication process."}})(r,i)}),r&&(0,a.jsxs)("div",{className:"bg-secondary-100 rounded-lg p-4 mb-6 text-left",children:[a.jsx("h3",{className:"text-sm font-medium text-secondary-900 mb-2",children:"Error Details:"}),(0,a.jsxs)("div",{className:"text-sm text-secondary-600 space-y-1",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Code:"})," ",r]}),i&&(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Message:"})," ",i]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Time:"})," ",new Date().toLocaleString()]})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{onClick:()=>{window.location.href="/auth/login"},className:"btn-primary w-full flex items-center justify-center space-x-2",children:[a.jsx(l,{className:"w-4 h-4"}),a.jsx("span",{children:"Try Again"})]}),(0,a.jsxs)("button",{onClick:()=>{e.push("/")},className:"btn-outline w-full flex items-center justify-center space-x-2",children:[a.jsx(c,{className:"w-4 h-4"}),a.jsx("span",{children:"Go to Home"})]})]}),a.jsx("div",{className:"mt-8 pt-6 border-t border-secondary-200",children:a.jsx("p",{className:"text-sm text-secondary-500",children:"If this problem persists, please contact support or try again later."})})]})})})}},402:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(8570).createProxy)(String.raw`C:\Choreo\frontend\src\app\auth\error\page.tsx#default`)},2029:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o,metadata:()=>i});var a=t(9510),s=t(5384),n=t.n(s);t(5023);let i={title:"Task Management - Choreo Full-Stack Sample",description:"A comprehensive task management application built with Next.js and deployed on WSO2 Choreo",keywords:["task management","choreo","nextjs","wso2","productivity"],authors:[{name:"WSO2 Choreo Team"}],viewport:"width=device-width, initial-scale=1",robots:"index, follow",openGraph:{title:"Task Management - Choreo Full-Stack Sample",description:"A comprehensive task management application built with Next.js and deployed on WSO2 Choreo",type:"website",locale:"en_US"},twitter:{card:"summary_large_image",title:"Task Management - Choreo Full-Stack Sample",description:"A comprehensive task management application built with Next.js and deployed on WSO2 Choreo"}};function o({children:e}){return a.jsx("html",{lang:"en",className:"h-full",children:a.jsx("body",{className:`${n().className} h-full`,children:a.jsx("div",{id:"root",className:"h-full",children:e})})})}},5023:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[935],()=>t(6690));module.exports=a})();