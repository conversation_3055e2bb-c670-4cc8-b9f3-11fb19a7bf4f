{"name": "eslint-plugin-import", "version": "2.32.0", "description": "Import with sanity.", "engines": {"node": ">=4"}, "main": "lib/index.js", "types": "index.d.ts", "directories": {"test": "tests"}, "files": ["*.md", "!{CONTRIBUTING,RELEASE}.md", "LICENSE", "docs", "lib", "config", "memo-parser/{*.js,LICENSE,*.md}", "index.d.ts"], "scripts": {"prebuild": "<PERSON><PERSON><PERSON> lib", "build": "babel --quiet --out-dir lib src", "postbuild": "npm run copy-metafiles", "copy-metafiles": "node --require babel-register ./scripts/copyMetafiles", "watch": "npm run tests-only -- -- --watch", "pretest": "linklocal", "posttest": "eslint . && npm run update:eslint-docs -- --check && markdownlint \"**/*.md\"", "mocha": "cross-env BABEL_ENV=test nyc mocha", "tests-only": "npm run mocha tests/src", "test": "npm run tests-only", "test-compiled": "npm run prepublish && BABEL_ENV=testCompiled mocha --compilers js:babel-register tests/src", "test-all": "node --require babel-register ./scripts/testAll", "test-examples": "npm run build && npm run test-example:legacy && npm run test-example:flat && npm run test-example:v9", "test-example:legacy": "cd examples/legacy && npm install && npm run lint", "test-example:flat": "cd examples/flat && npm install && npm run lint", "test-example:v9": "cd examples/v9 && npm install && npm run lint", "test-types": "npx --package typescript@latest tsc --noEmit index.d.ts", "prepublishOnly": "safe-publish-latest && npm run build", "prepublish": "not-in-publish || npm run prepublishOnly", "preupdate:eslint-docs": "npm run build", "update:eslint-docs": "eslint-doc-generator --rule-doc-title-format prefix-name --rule-doc-section-options false --rule-list-split meta.docs.category --ignore-config stage-0 --config-emoji recommended,☑️"}, "repository": {"type": "git", "url": "https://github.com/import-js/eslint-plugin-import"}, "keywords": ["eslint", "eslintplugin", "eslint-plugin", "es6", "jsnext", "modules", "import", "export"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/import-js/eslint-plugin-import/issues"}, "homepage": "https://github.com/import-js/eslint-plugin-import", "devDependencies": {"@angular-eslint/template-parser": "^13.5.0", "@eslint/import-test-order-redirect-scoped": "file:./tests/files/order-redirect-scoped", "@test-scope/some-module": "file:./tests/files/symlinked-module", "@types/eslint": "^8.56.12", "@typescript-eslint/parser": "^2.23.0 || ^3.3.0 || ^4.29.3 || ^5.10.0", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "babel-eslint": "=8.0.3 || ^8.2.6", "babel-plugin-istanbul": "^4.1.6", "babel-plugin-module-resolver": "^2.7.1", "babel-preset-airbnb": "^2.6.0", "babel-preset-flow": "^6.23.0", "babel-register": "^6.26.0", "babylon": "^6.18.0", "chai": "^4.3.10", "cross-env": "^4.0.0", "escope": "^3.6.0", "eslint": "^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9", "eslint-doc-generator": "^1.6.1", "eslint-import-resolver-node": "file:./resolvers/node", "eslint-import-resolver-typescript": "^1.0.2 || ^1.1.1", "eslint-import-resolver-webpack": "file:./resolvers/webpack", "eslint-import-test-order-redirect": "file:./tests/files/order-redirect", "eslint-module-utils": "file:./utils", "eslint-plugin-eslint-plugin": "^2.3.0", "eslint-plugin-import": "2.x", "eslint-plugin-json": "^2.1.2", "find-babel-config": "=1.2.0", "fs-copy-file-sync": "^1.1.1", "glob": "^7.2.3", "in-publish": "^2.0.1", "jackspeak": "=2.1.1", "jsonc-parser": "=3.2.0", "linklocal": "^2.8.2", "lodash.isarray": "^4.0.0", "markdownlint-cli": "~0.35", "mocha": "^3.5.3", "npm-which": "^3.0.1", "nyc": "^11.9.0", "redux": "^3.7.2", "rimraf": "^2.7.1", "safe-publish-latest": "^2.0.0", "sinon": "^2.4.1", "tmp": "^0.2.1", "typescript": "^2.8.1 || ~3.9.5 || ~4.5.2", "typescript-eslint-parser": "^15 || ^20 || ^22"}, "peerDependencies": {"eslint": "^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9"}, "dependencies": {"@rtsao/scc": "^1.1.0", "array-includes": "^3.1.9", "array.prototype.findlastindex": "^1.2.6", "array.prototype.flat": "^1.3.3", "array.prototype.flatmap": "^1.3.3", "debug": "^3.2.7", "doctrine": "^2.1.0", "eslint-import-resolver-node": "^0.3.9", "eslint-module-utils": "^2.12.1", "hasown": "^2.0.2", "is-core-module": "^2.16.1", "is-glob": "^4.0.3", "minimatch": "^3.1.2", "object.fromentries": "^2.0.8", "object.groupby": "^1.0.3", "object.values": "^1.2.1", "semver": "^6.3.1", "string.prototype.trimend": "^1.0.9", "tsconfig-paths": "^3.15.0"}}