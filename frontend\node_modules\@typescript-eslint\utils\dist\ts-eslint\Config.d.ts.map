{"version": 3, "file": "Config.d.ts", "sourceRoot": "", "sources": ["../../src/ts-eslint/Config.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EAAE,MAAM,IAAI,UAAU,EAAE,MAAM,UAAU,CAAC;AACrD,OAAO,KAAK,KAAK,kBAAkB,MAAM,iBAAiB,CAAC;AAC3D,OAAO,KAAK,EAAE,SAAS,IAAI,aAAa,EAAE,MAAM,aAAa,CAAC;AAC9D,OAAO,KAAK,EAAE,mBAAmB,EAAE,2BAA2B,EAAE,MAAM,QAAQ,CAAC;AAE/E,gBAAgB;AAChB,yBAAiB,YAAY,CAAC;IAC5B,KAAY,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACjC,KAAY,cAAc,GAAG,OAAO,GAAG,KAAK,GAAG,MAAM,CAAC;IACtD,KAAY,SAAS,GAAG,QAAQ,GAAG,cAAc,CAAC;IAElD,KAAY,mBAAmB,GAAG,CAAC,SAAS,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;IAE5D,KAAY,SAAS,GAAG,SAAS,GAAG,mBAAmB,CAAC;IACxD,KAAY,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;IAE7D,KAAY,wBAAwB,GAChC,KAAK,GACL,mCAAmC,CAAC,UAAU,GAC9C,UAAU,GACV,UAAU,GACV,mCAAmC,CAAC,WAAW,CAAC;IACpD,KAAY,2BAA2B,GACnC,mCAAmC,CAAC,KAAK,GACzC,mCAAmC,CAAC,IAAI,CAAC;IAC7C,KAAY,oBAAoB,GAC5B,wBAAwB,GACxB,2BAA2B,CAAC;IAEhC,UAAiB,aAAa;QAC5B,CAAC,IAAI,EAAE,MAAM,GAAG,oBAAoB,CAAC;KACtC;IACD,UAAiB,iBAAiB;QAChC,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC;KACzB;IAED,KAAY,aAAa,GAAG,kBAAkB,CAAC,aAAa,CAAC;IAE7D,UAAiB,UAAU;QACzB;;WAEG;QACH,IAAI,EAAE,MAAM,CAAC;QACb;;WAEG;QACH,OAAO,EAAE,MAAM,CAAC;KACjB;CACF;AAED,yBAAiB,aAAa,CAAC;IAC7B,MAAM,MAAM,iBAAiB,GAAG,YAAY,CAAC,iBAAiB,CAAC;IAC/D,MAAM,MAAM,aAAa,GAAG,YAAY,CAAC,aAAa,CAAC;IACvD,MAAM,MAAM,oBAAoB,GAAG,YAAY,CAAC,oBAAoB,CAAC;IACrE,MAAM,MAAM,wBAAwB,GAAG,YAAY,CAAC,wBAAwB,CAAC;IAC7E,MAAM,MAAM,aAAa,GAAG,YAAY,CAAC,aAAa,CAAC;IACvD,MAAM,MAAM,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;IAC/C,MAAM,MAAM,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;IAC/C,MAAM,MAAM,mBAAmB,GAAG,YAAY,CAAC,mBAAmB,CAAC;IACnE,MAAM,MAAM,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC;IACnD,MAAM,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;IAC7C,MAAM,MAAM,cAAc,GAAG,YAAY,CAAC,cAAc,CAAC;IAGzD,UAAU,UAAU;QAClB,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB;;WAEG;QACH,GAAG,CAAC,EAAE,iBAAiB,CAAC;QACxB;;WAEG;QACH,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC;QAC5B;;WAEG;QACH,OAAO,CAAC,EAAE,aAAa,CAAC;QACxB;;WAEG;QACH,cAAc,CAAC,EAAE,OAAO,CAAC;QACzB;;WAEG;QACH,SAAS,CAAC,EAAE,cAAc,EAAE,CAAC;QAC7B;;WAEG;QACH,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QACvB;;WAEG;QACH,aAAa,CAAC,EAAE,aAAa,CAAC;QAC9B;;WAEG;QACH,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC;QACnB;;WAEG;QACH,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB;;WAEG;QACH,6BAA6B,CAAC,EAAE,OAAO,CAAC;QACxC;;WAEG;QACH,KAAK,CAAC,EAAE,WAAW,CAAC;QACpB;;WAEG;QACH,QAAQ,CAAC,EAAE,2BAA2B,CAAC;KACxC;IAED,MAAM,WAAW,cAAe,SAAQ,UAAU;QAChD,aAAa,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC;QAClC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC;KAC1B;IAED,MAAM,WAAW,MAAO,SAAQ,UAAU;QACxC;;WAEG;QACH,cAAc,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC;QACnC;;WAEG;QACH,IAAI,CAAC,EAAE,OAAO,CAAC;KAChB;;CACF;AAED,yBAAiB,UAAU,CAAC;IAC1B,KAAY,WAAW,GAAG,kBAAkB,CAAC,WAAW,CAAC;IACzD,KAAY,aAAa,GAAG,YAAY,CAAC,aAAa,CAAC;IACvD,KAAY,MAAM,GAAG,UAAU,CAAC,iBAAiB,CAAC;IAClD,KAAY,aAAa,GAAG,YAAY,CAAC,aAAa,CAAC;IACvD,KAAY,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC;IACjD,KAAY,SAAS,GAAG,aAAa,CAAC,oBAAoB,CAAC;IAC3D,KAAY,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;IAC/C,KAAY,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;IAC/C,KAAY,mBAAmB,GAAG,YAAY,CAAC,mBAAmB,CAAC;IACnE,KAAY,KAAK,GAAG,YAAY,CAAC,WAAW,CAAC;IAC7C,KAAY,QAAQ,GAAG,2BAA2B,CAAC;IACnD,KAAY,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;IAC7C,KAAY,cAAc,GAAG,YAAY,CAAC,cAAc,CAAC;IACzD,KAAY,UAAU,GAAG,UAAU,GAAG,kBAAkB,CAAC,UAAU,CAAC;IAEpE,UAAiB,aAAa;QAC5B,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,GAAG,WAAW,CAAC;KACrC;IACD,UAAiB,MAAM;QACrB;;;WAGG;QACH,OAAO,CAAC,EAAE,aAAa,CAAC;QACxB;;WAEG;QACH,IAAI,CAAC,EAAE;aAAG,CAAC,IAAI,MAAM,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,SAAS;SAAE,CAAC;QAC/D;;;WAGG;QACH,UAAU,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC;QAC5D;;;;;WAKG;QACH,KAAK,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,mBAAmB,CAAC,GAAG,SAAS,CAAC;KACzD;IACD,UAAiB,OAAO;QACtB;;;;;WAKG;QACH,CAAC,WAAW,EAAE,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;KAChD;IAED,UAAiB,aAAa;QAC5B;;WAEG;QACH,cAAc,CAAC,EAAE,OAAO,CAAC;QACzB;;;;;WAKG;QACH,6BAA6B,CAAC,EAC1B,OAAO,GACP,YAAY,CAAC,QAAQ,GACrB,YAAY,CAAC,cAAc,CAAC;QAChC;;;;;;WAMG;QACH,yBAAyB,CAAC,EACtB,YAAY,CAAC,QAAQ,GACrB,YAAY,CAAC,cAAc,CAAC;KACjC;IAED,UAAiB,eAAe;QAC9B;;;;;WAKG;QACH,WAAW,CAAC,EAAE,WAAW,GAAG,SAAS,CAAC;QACtC;;WAEG;QACH,OAAO,CAAC,EAAE,aAAa,GAAG,SAAS,CAAC;QACpC;;;;;;;WAOG;QACH,MAAM,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;QAC5B;;;WAGG;QACH,aAAa,CAAC,EAAE,aAAa,GAAG,SAAS,CAAC;QAC1C;;;;;;;;;;WAUG;QACH,UAAU,CAAC,EAAE,UAAU,GAAG,SAAS,CAAC;KACrC;IAID,UAAiB,MAAM;QACrB;;;;;;WAMG;QACH,QAAQ,CAAC,EAAE,MAAM,CAAC;QAElB;;;WAGG;QACH,KAAK,CAAC,EAAE,CACJ,MAAM,GACN,MAAM,EAAE,CACX,EAAE,CAAC;QACJ;;;WAGG;QACH,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC;QACnB;;WAEG;QACH,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB;;WAEG;QACH,eAAe,CAAC,EAAE,eAAe,CAAC;QAClC;;WAEG;QACH,aAAa,CAAC,EAAE,aAAa,CAAC;QAC9B;;WAEG;QACH,IAAI,CAAC,EAAE,MAAM,CAAC;QACd;;;WAGG;QACH,OAAO,CAAC,EAAE,OAAO,CAAC;QAClB;;;;WAIG;QACH,SAAS,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;QAC/B;;;WAGG;QACH,KAAK,CAAC,EAAE,KAAK,CAAC;QACd;;WAEG;QACH,QAAQ,CAAC,EAAE,QAAQ,CAAC;KACrB;IACD,KAAY,WAAW,GAAG,MAAM,EAAE,CAAC;IACnC,KAAY,aAAa,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;IACjD,KAAY,UAAU,GAAG,WAAW,GAAG,aAAa,CAAC;CACtD"}