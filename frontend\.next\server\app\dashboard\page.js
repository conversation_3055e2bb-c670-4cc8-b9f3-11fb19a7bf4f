(()=>{var e={};e.id=702,e.ids=[702],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2237:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>d,pages:()=>c,routeModule:()=>h,tree:()=>u}),r(8256),r(2834),r(2029),r(5866);var s=r(3191),n=r(8716),a=r(7922),o=r.n(a),i=r(5231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let u=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,8256,23)),"C:\\Choreo\\frontend\\src\\app\\dashboard\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,2834)),"C:\\Choreo\\frontend\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,2029)),"C:\\Choreo\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Choreo\\frontend\\src\\app\\dashboard\\page.tsx"],d="/dashboard/page",m={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},4015:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2994,23)),Promise.resolve().then(r.t.bind(r,6114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,9671,23)),Promise.resolve().then(r.t.bind(r,1868,23)),Promise.resolve().then(r.t.bind(r,4759,23))},469:()=>{},7760:(e,t,r)=>{Promise.resolve().then(r.bind(r,1469))},5303:()=>{},5047:(e,t,r)=>{"use strict";var s=r(7389);r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},1469:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(326),n=r(7577),a=r(5047);function o(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)e[s]=r[s]}return e}var i=function e(t,r){function s(e,s,n){if("undefined"!=typeof document){"number"==typeof(n=o({},r,n)).expires&&(n.expires=new Date(Date.now()+864e5*n.expires)),n.expires&&(n.expires=n.expires.toUTCString()),e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var a="";for(var i in n)n[i]&&(a+="; "+i,!0!==n[i]&&(a+="="+n[i].split(";")[0]));return document.cookie=e+"="+t.write(s,e)+a}}return Object.create({set:s,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var r=document.cookie?document.cookie.split("; "):[],s={},n=0;n<r.length;n++){var a=r[n].split("="),o=a.slice(1).join("=");try{var i=decodeURIComponent(a[0]);if(s[i]=t.read(o,i),e===i)break}catch(e){}}return e?s[e]:s}},remove:function(e,t){s(e,"",o({},t,{expires:-1}))},withAttributes:function(t){return e(this.converter,o({},this.attributes,t))},withConverter:function(t){return e(o({},this.converter,t),this.attributes)}},{attributes:{value:Object.freeze(r)},converter:{value:Object.freeze(t)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"});class l{constructor(){this.user=null,this.isAuthenticated=!1}static getInstance(){return l.instance||(l.instance=new l),l.instance}login(e){let t="/auth/login",r=e?`${t}?redirect=${encodeURIComponent(e)}`:t;window.location.href=r}logout(){let e=i.get("session_hint"),t=e?`/auth/logout?session_hint=${e}`:"/auth/logout";this.user=null,this.isAuthenticated=!1,this.clearStoredUserData(),window.location.href=t}getUserInfoFromCookie(){try{let e=i.get("userinfo");if(!e)return null;let t=JSON.parse(atob(e));return i.remove("userinfo",{path:"/"}),this.user={id:t.sub,email:t.email,name:t.name||`${t.given_name||""} ${t.family_name||""}`.trim(),username:t.preferred_username,groups:t.groups||[],roles:t.roles||[],profileComplete:!!(t.name&&t.email)},this.isAuthenticated=!0,this.storeUserData(this.user),this.user}catch(e){return console.error("Error parsing user info cookie:",e),null}}async checkAuthStatus(){try{let e=await fetch("/auth/userinfo",{method:"GET",credentials:"include",headers:{Accept:"application/json"}});if(e.ok){let t=await e.json();return this.user={id:t.sub,email:t.email,name:t.name||`${t.given_name||""} ${t.family_name||""}`.trim(),username:t.preferred_username,groups:t.groups||[],roles:t.roles||[],profileComplete:!!(t.name&&t.email)},this.isAuthenticated=!0,this.storeUserData(this.user),{isAuthenticated:!0,user:this.user}}if(401===e.status)return this.user=null,this.isAuthenticated=!1,this.clearStoredUserData(),{isAuthenticated:!1,user:null};throw Error(`Auth check failed with status: ${e.status}`)}catch(t){console.error("Error checking auth status:",t);let e=this.getStoredUserData();if(e)return this.user=e,this.isAuthenticated=!0,{isAuthenticated:!0,user:e};return{isAuthenticated:!1,user:null}}}getCurrentUser(){if(this.user)return this.user;let e=this.getStoredUserData();return e?(this.user=e,this.isAuthenticated=!0,e):null}getIsAuthenticated(){return this.isAuthenticated||!!this.getStoredUserData()}handleSessionExpiry(){console.log("Session expired, redirecting to login..."),this.user=null,this.isAuthenticated=!1,this.clearStoredUserData(),this.login(window.location.pathname)}storeUserData(e){try{localStorage.setItem("user",JSON.stringify(e)),localStorage.setItem("lastLogin",new Date().toISOString())}catch(e){console.error("Error storing user data:",e)}}getStoredUserData(){try{let e=localStorage.getItem("user");if(e)return JSON.parse(e)}catch(e){console.error("Error retrieving stored user data:",e)}return null}clearStoredUserData(){try{localStorage.removeItem("user"),localStorage.removeItem("lastLogin")}catch(e){console.error("Error clearing stored user data:",e)}}}function u({size:e="medium",className:t="",text:r}){return(0,s.jsxs)("div",{className:`flex flex-col items-center justify-center ${t}`,children:[s.jsx("div",{className:`spinner ${{small:"w-4 h-4",medium:"w-6 h-6",large:"w-8 h-8"}[e]}`}),r&&s.jsx("p",{className:"mt-2 text-sm text-secondary-600",children:r})]})}function c({children:e}){let[t,r]=(0,n.useState)(!0),[o,i]=(0,n.useState)(!1);return((0,a.useRouter)(),t)?s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-secondary-50",children:s.jsx(u,{size:"large",text:"Checking authentication..."})}):o?s.jsx(s.Fragment,{children:e}):s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-secondary-50",children:s.jsx(u,{size:"large",text:"Redirecting to login..."})})}l.getInstance()},2834:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(8570).createProxy)(String.raw`C:\Choreo\frontend\src\app\dashboard\layout.tsx#default`)},8256:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError: \n  \x1b[38;2;255;30;30m\xd7\x1b[0m the name `error` is defined multiple times\n    ╭─[\x1b[38;2;92;157;255;1;4mC:\\Choreo\\frontend\\src\\app\\dashboard\\page.tsx\x1b[0m:15:1]\n \x1b[2m15\x1b[0m │ export default function DashboardPage() {\n \x1b[2m16\x1b[0m │   // State management\n \x1b[2m17\x1b[0m │   const [user, setUser] = useState<User | null>(null)\n \x1b[2m18\x1b[0m │   const { toasts, removeToast, success, error } = useToast()\n    \xb7 \x1b[38;2;246;87;248m                                        ──┬──\x1b[0m\n    \xb7                                           \x1b[38;2;246;87;248m╰── \x1b[38;2;246;87;248mprevious definition of `error` here\x1b[0m\x1b[0m\n \x1b[2m19\x1b[0m │   const [tasks, setTasks] = useState<Task[]>([])\n \x1b[2m20\x1b[0m │   const [taskStats, setTaskStats] = useState<TaskStats | null>(null)\n \x1b[2m21\x1b[0m │   const [filters, setFilters] = useState<TaskFilters>({})\n \x1b[2m22\x1b[0m │   const [isLoading, setIsLoading] = useState(true)\n \x1b[2m23\x1b[0m │   const [error, setError] = useState<string | null>(null)\n    \xb7 \x1b[38;2;30;201;212m         ──┬──\x1b[0m\n    \xb7            \x1b[38;2;30;201;212m╰── \x1b[38;2;30;201;212m`error` redefined here\x1b[0m\x1b[0m\n \x1b[2m24\x1b[0m │   const [showTaskForm, setShowTaskForm] = useState(false)\n \x1b[2m25\x1b[0m │   const [editingTask, setEditingTask] = useState<Task | null>(null)\n \x1b[2m26\x1b[0m │   const [searchQuery, setSearchQuery] = useState('')\n    ╰────\n")},2029:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>o});var s=r(9510),n=r(5384),a=r.n(n);r(5023);let o={title:"Task Management - Choreo Full-Stack Sample",description:"A comprehensive task management application built with Next.js and deployed on WSO2 Choreo",keywords:["task management","choreo","nextjs","wso2","productivity"],authors:[{name:"WSO2 Choreo Team"}],viewport:"width=device-width, initial-scale=1",robots:"index, follow",openGraph:{title:"Task Management - Choreo Full-Stack Sample",description:"A comprehensive task management application built with Next.js and deployed on WSO2 Choreo",type:"website",locale:"en_US"},twitter:{card:"summary_large_image",title:"Task Management - Choreo Full-Stack Sample",description:"A comprehensive task management application built with Next.js and deployed on WSO2 Choreo"}};function i({children:e}){return s.jsx("html",{lang:"en",className:"h-full",children:s.jsx("body",{className:`${a().className} h-full`,children:s.jsx("div",{id:"root",className:"h-full",children:e})})})}},5023:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[935],()=>r(2237));module.exports=s})();